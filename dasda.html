<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <title>graph 随机分布同心圆光晕</title>
    <!-- Vue -->
    <script src="https://cdn.jsdelivr.net/npm/vue@3.2.31/dist/vue.global.js"></script>
    <!-- ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.3.3/dist/echarts.min.js"></script>
    <style>
      body {
        margin: 0;
        min-height: 100vh;
        background: linear-gradient(135deg, #0a1a3f, #002b55);
        font-family: Arial, 'Microsoft Yahei', sans-serif;
      }
      #app {
        width: 100vw;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      #main {
        width: 900px;
        height: 520px;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div id="main" ref="mainChart"></div>
    </div>

    <script>
      const { createApp, ref, onMounted, onUnmounted } = Vue

      createApp({
        setup() {
          const mainChart = ref(null)
          let chartInstance = null

          const selectedPeriod = ref('weekData')
          const colorList = [
            '#5B8FF9',
            '#5AD8A6',
            '#5D7092',
            '#F6BD16',
            '#6F5EF9',
            '#6DC8EC',
            '#945FB9',
            '#FF9845',
            '#1E9493',
            '#FF99C3',
          ]
          const periodData = ref({
            weekData: [
              { name: '服务态度', value: 24 },
              { name: '设施设备', value: 30 },
              { name: '环境卫生', value: 24 },
              { name: '信息咨询', value: 15 },
              { name: '安检流程', value: 20 },
              { name: '导向标识', value: 18 },
              { name: '其它', value: 22 },
            ],
          })

          const hex2rgba = (hex, a = 1) => {
            const c = hex.replace('#', '')
            const bigint = parseInt(c, 16)
            const r = (bigint >> 16) & 255
            const g = (bigint >> 8) & 255
            const b = bigint & 255
            return `rgba(${r},${g},${b},${a})`
          }

          const sizeScale = (v, vmin, vmax, minR, maxR) => {
            if (vmin === vmax) return (minR + maxR) / 2
            return minR + ((v - vmin) * (maxR - minR)) / (vmax - vmin)
          }

          // 生成随机位置，避免过度重叠
          const calcPositions = (n, w, h) => {
            const margin = 120 // 边距
            const minDist = 150 // 最小间距
            const pts = []
            for (let i = 0; i < n; i++) {
              let valid = false,
                x,
                y
              while (!valid) {
                x = margin + Math.random() * (w - 2 * margin)
                y = margin + Math.random() * (h - 2 * margin)
                valid = pts.every((p) => Math.hypot(p.x - x, p.y - y) > minDist)
              }
              pts.push({ x, y })
            }
            return pts
          }

          const buildNodes = () => {
            const list = periodData.value[selectedPeriod.value]
            const values = list.map((d) => d.value)
            const vmin = Math.min(...values)
            const vmax = Math.max(...values)
            const w = chartInstance.getWidth()
            const h = chartInstance.getHeight()
            const pos = calcPositions(list.length, w, h)
            return list.map((item, i) => {
              const base = colorList[i % colorList.length]
              const inner = sizeScale(item.value, vmin, vmax, 90, 140)
              const outer = inner * 1.25
              return {
                id: String(i),
                name: item.name,
                value: item.value,
                x: pos[i].x,
                y: pos[i].y,
                innerSize: inner,
                outerSize: outer,
                baseColor: base,
              }
            })
          }

          const makeOption = (nodes) => {
            return {
              backgroundColor: 'transparent',
              animation: true,
              xAxis: { show: false },
              yAxis: { show: false },
              grid: { left: 0, right: 0, top: 0, bottom: 0 },
              series: [
                {
                  type: 'graph',
                  layout: 'none',
                  roam: true,
                  focusNodeAdjacency: false,
                  zlevel: 1,
                  data: nodes.flatMap((n) => [
                    {
                      id: 'halo-' + n.id,
                      x: n.x,
                      y: n.y,
                      symbol: 'circle',
                      symbolSize: n.outerSize,
                      silent: true,
                      emphasis: { disabled: true },
                      z: 1,
                      itemStyle: {
                        color: new echarts.graphic.RadialGradient(
                          0.5,
                          0.5,
                          0.8,
                          [
                            { offset: 0, color: hex2rgba(n.baseColor, 0.22) },
                            { offset: 0.6, color: hex2rgba(n.baseColor, 0.16) },
                            { offset: 1, color: hex2rgba(n.baseColor, 0.08) },
                          ]
                        ),
                      },
                    },
                    {
                      id: 'core-' + n.id,
                      name: n.name,
                      value: n.value,
                      x: n.x,
                      y: n.y,
                      symbol: 'circle',
                      symbolSize: n.innerSize,
                      z: 2,
                      itemStyle: {
                        color: new echarts.graphic.RadialGradient(
                          0.5,
                          0.5,
                          0.8,
                          [
                            { offset: 0, color: hex2rgba(n.baseColor, 0.1) },
                            { offset: 0.7, color: hex2rgba(n.baseColor, 0.3) },
                            { offset: 1, color: hex2rgba(n.baseColor, 0.5) },
                          ]
                        ),
                        borderColor: hex2rgba('#FFFFFF', 0.35),
                        borderWidth: 2,
                        shadowBlur: 18,
                        shadowColor: hex2rgba(n.baseColor, 0.85),
                      },
                      label: {
                        show: true,
                        position: 'inside',
                        formatter: (p) => `{val|${p.value}}\n{name|${p.name}}`,
                        rich: {
                          val: {
                            color: '#fff',
                            fontSize: 26,
                            fontWeight: 'bold',
                            lineHeight: 30,
                            textShadowColor: 'rgba(0,0,0,0.5)',
                            textShadowBlur: 6,
                          },
                          name: {
                            color: 'rgba(255,255,255,0.9)',
                            fontSize: 16,
                            lineHeight: 22,
                            textShadowColor: 'rgba(0,0,0,0.45)',
                            textShadowBlur: 6,
                          },
                        },
                      },
                      emphasis: {
                        focus: 'none',
                        scale: true,
                        itemStyle: {
                          shadowBlur: 30,
                          shadowColor: 'rgba(255,255,255,0.85)',
                        },
                      },
                    },
                  ]),
                },
              ],
            }
          }

          const render = () => {
            const nodes = buildNodes()
            const option = makeOption(nodes)
            chartInstance.setOption(option, true)
          }

          const initChart = () => {
            if (!mainChart.value) return
            chartInstance = echarts.init(mainChart.value)
            render()
          }

          const handleResize = () => {
            if (!chartInstance) return
            chartInstance.resize()
            render()
          }

          onMounted(() => {
            initChart()
            window.addEventListener('resize', handleResize)
          })
          onUnmounted(() => {
            window.removeEventListener('resize', handleResize)
            chartInstance && chartInstance.dispose()
          })

          return { mainChart }
        },
      }).mount('#app')
    </script>
  </body>
</html>
